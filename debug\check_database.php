<?php
/**
 * Check Database Records for File Management Issues
 */

require_once '../api/config/config.php';
require_once '../api/config/database.php';

echo "<h1>Database File Records Check</h1>\n";
echo "<pre>\n";

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    if (!$pdo) {
        throw new Exception('Database connection failed');
    }
    
    echo "=== DATABASE CONNECTION ===\n";
    echo "✅ Database connected successfully\n\n";
    
    // Check documents table structure
    echo "=== DOCUMENTS TABLE STRUCTURE ===\n";
    $stmt = $pdo->query("DESCRIBE documents");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo sprintf("%-20s %-15s %-10s %-10s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'], 
            $column['Key']
        );
    }
    
    echo "\n=== FILE RECORDS IN DATABASE ===\n";
    $stmt = $pdo->query("SELECT id, original_name, storage_path, file_name, file_size, created_at FROM documents ORDER BY created_at DESC LIMIT 10");
    $files = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($files)) {
        echo "❌ No file records found in database\n";
    } else {
        echo "Found " . count($files) . " file records:\n\n";
        
        foreach ($files as $file) {
            echo "ID: " . $file['id'] . "\n";
            echo "Original Name: " . $file['original_name'] . "\n";
            echo "Storage Path: " . $file['storage_path'] . "\n";
            echo "File Name: " . $file['file_name'] . "\n";
            echo "File Size: " . $file['file_size'] . " bytes\n";
            echo "Created: " . $file['created_at'] . "\n";
            
            // Check if physical file exists
            $physical_paths = [
                '../uploads/' . $file['storage_path'],
                '../uploads/pdfs/' . $file['file_name'],
                '../uploads/' . $file['file_name']
            ];
            
            $file_found = false;
            foreach ($physical_paths as $path) {
                if (file_exists($path)) {
                    echo "✅ Physical file found at: " . $path . "\n";
                    echo "   Actual size: " . filesize($path) . " bytes\n";
                    $file_found = true;
                    break;
                }
            }
            
            if (!$file_found) {
                echo "❌ Physical file NOT FOUND\n";
                echo "   Searched paths:\n";
                foreach ($physical_paths as $path) {
                    echo "   - " . $path . "\n";
                }
            }
            
            echo "---\n";
        }
    }
    
    echo "\n=== STORAGE USAGE TABLE ===\n";
    $stmt = $pdo->query("SELECT * FROM storage_usage");
    $usage = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($usage)) {
        echo "❌ No storage usage records found\n";
    } else {
        foreach ($usage as $record) {
            echo "User ID: " . $record['user_id'] . "\n";
            echo "Total Used: " . $record['total_used'] . " bytes (" . round($record['total_used'] / 1024 / 1024, 2) . " MB)\n";
            echo "Document Count: " . $record['document_count'] . "\n";
            echo "Last Updated: " . $record['last_updated'] . "\n";
            echo "---\n";
        }
    }
    
    echo "\n=== PHYSICAL FILES IN UPLOAD DIRECTORY ===\n";
    $upload_dirs = ['../uploads/', '../uploads/pdfs/'];
    
    foreach ($upload_dirs as $dir) {
        if (is_dir($dir)) {
            echo "Directory: " . $dir . "\n";
            $files = scandir($dir);
            $file_count = 0;
            
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..' && is_file($dir . $file)) {
                    echo "  - " . $file . " (" . filesize($dir . $file) . " bytes)\n";
                    $file_count++;
                }
            }
            
            if ($file_count === 0) {
                echo "  (No files found)\n";
            }
            echo "\n";
        } else {
            echo "❌ Directory does not exist: " . $dir . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>\n";
?>
