<?php
/**
 * Verify File Paths and Naming Conventions
 * Check exact file locations and database records
 */

require_once '../api/config/config.php';
require_once '../api/config/database.php';

echo "<h1>File Paths Verification</h1>\n";
echo "<pre>\n";

echo "=== CONFIGURATION ===\n";
echo "UPLOAD_DIR: " . UPLOAD_DIR . "\n";
echo "BASE_URL: " . BASE_URL . "\n";
echo "Current working directory: " . getcwd() . "\n";
echo "Script location: " . __FILE__ . "\n\n";

// Check physical files
echo "=== PHYSICAL FILES ===\n";
$upload_dirs = [
    '../uploads/',
    '../uploads/pdfs/',
    UPLOAD_DIR,
    UPLOAD_DIR . 'pdfs/'
];

foreach ($upload_dirs as $dir) {
    echo "Checking directory: " . $dir . "\n";
    $real_path = realpath($dir);
    echo "  Real path: " . ($real_path ?: 'NOT FOUND') . "\n";
    
    if (is_dir($dir)) {
        $files = scandir($dir);
        $file_count = 0;
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..' && is_file($dir . $file)) {
                echo "  - " . $file . "\n";
                echo "    Size: " . filesize($dir . $file) . " bytes\n";
                echo "    URL encoded: " . urlencode($file) . "\n";
                echo "    URL decoded: " . urldecode($file) . "\n";
                $file_count++;
            }
        }
        echo "  Total files: " . $file_count . "\n";
    } else {
        echo "  ❌ Directory does not exist\n";
    }
    echo "\n";
}

// Check database records
echo "=== DATABASE RECORDS ===\n";
try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    if ($pdo) {
        $stmt = $pdo->query("
            SELECT id, original_name, storage_path, file_name, file_size, created_at 
            FROM documents 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        $files = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Found " . count($files) . " file records:\n\n";
        
        foreach ($files as $file) {
            echo "Record ID: " . $file['id'] . "\n";
            echo "  Original name: " . $file['original_name'] . "\n";
            echo "  File name: " . $file['file_name'] . "\n";
            echo "  Storage path: " . $file['storage_path'] . "\n";
            echo "  File size: " . $file['file_size'] . " bytes\n";
            echo "  Created: " . $file['created_at'] . "\n";
            
            // Check if physical file exists
            $possible_paths = [
                '../uploads/' . $file['storage_path'],
                '../uploads/pdfs/' . $file['file_name'],
                '../uploads/' . $file['file_name'],
                UPLOAD_DIR . $file['storage_path'],
                UPLOAD_DIR . 'pdfs/' . $file['file_name']
            ];
            
            $found = false;
            foreach ($possible_paths as $path) {
                if (file_exists($path)) {
                    echo "  ✅ Physical file found: " . $path . "\n";
                    echo "     Actual size: " . filesize($path) . " bytes\n";
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                echo "  ❌ Physical file NOT FOUND\n";
                echo "     Searched paths:\n";
                foreach ($possible_paths as $path) {
                    echo "       - " . $path . "\n";
                }
            }
            
            echo "  ---\n";
        }
    } else {
        echo "❌ Database connection failed\n";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

// Test URL encoding scenarios
echo "\n=== URL ENCODING TESTS ===\n";
$test_filenames = [
    "Order Details-1-1_1749996936.pdf",
    "Receipt-2603-5096_1749990373.pdf",
    "Order Details_1749993933.pdf"
];

foreach ($test_filenames as $filename) {
    echo "Testing filename: " . $filename . "\n";
    echo "  URL encoded: " . urlencode($filename) . "\n";
    echo "  Double encoded: " . urlencode(urlencode($filename)) . "\n";
    echo "  Basename: " . basename($filename) . "\n";
    echo "  Basename of encoded: " . basename(urlencode($filename)) . "\n";
    echo "  Decoded: " . urldecode($filename) . "\n";
    
    // Check if file exists with different variations
    $variations = [
        $filename,
        urlencode($filename),
        urldecode($filename),
        basename($filename)
    ];
    
    foreach ($variations as $variation) {
        $path = '../uploads/pdfs/' . $variation;
        if (file_exists($path)) {
            echo "  ✅ Found with variation: " . $variation . "\n";
        }
    }
    echo "  ---\n";
}

// Test API path resolution
echo "\n=== API PATH RESOLUTION ===\n";
$api_dir = dirname(__FILE__) . '/../api/files/';
echo "API files directory: " . $api_dir . "\n";
echo "API files real path: " . realpath($api_dir) . "\n";

$upload_from_api = $api_dir . '../uploads/pdfs/';
echo "Upload dir from API perspective: " . $upload_from_api . "\n";
echo "Upload dir real path: " . realpath($upload_from_api) . "\n";

echo "\n=== RECOMMENDATIONS ===\n";
echo "1. Ensure all file operations use consistent path resolution\n";
echo "2. Always decode URL-encoded filenames before file operations\n";
echo "3. Use basename() to prevent directory traversal\n";
echo "4. Verify database storage_path matches actual file locations\n";
echo "5. Test with Android app using exact filename formats\n";

echo "</pre>\n";
?>
