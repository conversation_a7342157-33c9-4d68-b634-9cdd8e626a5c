<?php
/**
 * File Upload API
 * Replaces Firebase Storage functionality
 * 
 * @package MtcInvoice
 * @version 1.0
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../models/User.php';
require_once '../utils/jwt.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendErrorResponse('Method not allowed', 405);
}

try {
    // Enhanced logging for debugging
    error_log("=== FILE UPLOAD DEBUG START ===");
    error_log("Request method: " . $_SERVER['REQUEST_METHOD']);
    error_log("Content type: " . ($_SERVER['CONTENT_TYPE'] ?? 'not set'));
    error_log("Content length: " . ($_SERVER['CONTENT_LENGTH'] ?? 'not set'));
    error_log("Files array: " . print_r($_FILES, true));

    // Authentication required
    $user = JWT::requireAuth(JWT_SECRET_KEY);
    error_log("User authenticated: " . $user['user_id']);

    // Check if file was uploaded
    if (!isset($_FILES['file'])) {
        error_log("ERROR: No file in _FILES array");
        sendErrorResponse('No file uploaded', 400);
    }

    if ($_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        error_log("ERROR: File upload error code: " . $_FILES['file']['error']);
        $error_messages = [
            UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];
        $error_msg = $error_messages[$_FILES['file']['error']] ?? 'Unknown upload error';
        sendErrorResponse($error_msg, 400);
    }

    $file = $_FILES['file'];
    error_log("File details: " . print_r($file, true));
    
    // Validate file size
    if ($file['size'] > MAX_FILE_SIZE) {
        sendErrorResponse('File size exceeds maximum allowed size (' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB)', 400);
    }
    
    // Get file extension
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    // Validate file type
    if (!in_array($file_extension, ALLOWED_FILE_TYPES)) {
        sendErrorResponse('File type not allowed. Allowed types: ' . implode(', ', ALLOWED_FILE_TYPES), 400);
    }
    
    // Create upload directory if it doesn't exist - use relative path
    $upload_dir = UPLOAD_DIR . 'pdfs/';
    error_log("Upload directory: " . $upload_dir);

    // Resolve absolute path for directory operations
    $absolute_upload_dir = realpath(dirname(__FILE__) . '/' . $upload_dir);
    if (!$absolute_upload_dir) {
        // If realpath fails, construct the path manually
        $absolute_upload_dir = dirname(__FILE__) . '/' . $upload_dir;
    }
    error_log("Upload directory absolute path: " . $absolute_upload_dir);

    if (!is_dir($absolute_upload_dir)) {
        error_log("Upload directory doesn't exist, creating...");
        if (mkdir($absolute_upload_dir, 0755, true)) {
            error_log("Upload directory created successfully");
        } else {
            error_log("ERROR: Failed to create upload directory");
            sendErrorResponse('Failed to create upload directory', 500);
        }
    } else {
        error_log("Upload directory exists");
    }

    // Check directory permissions
    if (file_exists($absolute_upload_dir)) {
        error_log("Directory permissions: " . substr(sprintf('%o', fileperms($absolute_upload_dir)), -4));
        error_log("Directory writable: " . (is_writable($absolute_upload_dir) ? 'YES' : 'NO'));
    }

    // Generate unique filename
    $original_name = pathinfo($file['name'], PATHINFO_FILENAME);
    $timestamp = time();
    $unique_filename = $original_name . '_' . $timestamp . '.' . $file_extension;
    $file_path = $absolute_upload_dir . '/' . $unique_filename;

    error_log("Generated filename: " . $unique_filename);
    error_log("Full file path: " . $file_path);
    error_log("Temp file: " . $file['tmp_name']);
    error_log("Temp file exists: " . (file_exists($file['tmp_name']) ? 'YES' : 'NO'));
    error_log("Temp file size: " . (file_exists($file['tmp_name']) ? filesize($file['tmp_name']) : 'N/A'));

    // Move uploaded file
    error_log("Attempting to move uploaded file...");
    if (!move_uploaded_file($file['tmp_name'], $file_path)) {
        error_log("ERROR: move_uploaded_file() failed");
        error_log("Source: " . $file['tmp_name']);
        error_log("Destination: " . $file_path);
        error_log("Last error: " . print_r(error_get_last(), true));
        sendErrorResponse('Failed to save uploaded file', 500);
    }

    error_log("File moved successfully");
    error_log("File exists after move: " . (file_exists($file_path) ? 'YES' : 'NO'));
    error_log("File size after move: " . (file_exists($file_path) ? filesize($file_path) : 'N/A'));
    
    // Calculate file hash for deduplication
    $file_hash = hash_file('sha256', $file_path);
    
    // Get file info
    $file_info = [
        'id' => generatePushKey(),
        'original_name' => $file['name'],
        'stored_name' => $unique_filename,
        'file_path' => $file_path,
        'relative_path' => 'pdfs/' . $unique_filename,
        'file_size' => $file['size'],
        'file_type' => $file['type'],
        'file_extension' => $file_extension,
        'file_hash' => $file_hash,
        'uploaded_by' => $user['user_id'],
        'uploaded_at' => date('Y-m-d H:i:s'),
        'download_url' => BASE_URL . 'files/download.php?file=' . urlencode($unique_filename)
    ];

    // Store file metadata in database
    try {
        $database = new Database();
        $pdo = $database->getConnection();

        if (!$pdo) {
            error_log("ERROR: Database connection failed");
            throw new Exception('Database connection failed');
        }

        error_log("Database connection successful");

        // Check if file with same hash already exists for this user
        $stmt = $pdo->prepare("SELECT id FROM documents WHERE file_hash = ? AND user_id = ?");
        $stmt->execute([$file_hash, $user['user_id']]);

        if ($stmt->fetch()) {
            // File already exists, delete the uploaded duplicate
            unlink($file_path);
            error_log("Duplicate file detected, deleted uploaded file");
            sendErrorResponse('File already exists', 409);
        }

        error_log("No duplicate file found, proceeding with database insertion");

        // Insert file metadata into documents table
        error_log("Preparing database insertion with data: " . json_encode([
            'id' => $file_info['id'],
            'user_id' => $user['user_id'],
            'original_name' => $file_info['original_name'],
            'storage_path' => $file_info['relative_path'],
            'file_name' => $file_info['stored_name'],
            'file_size' => $file_info['file_size'],
            'file_type' => $file_info['file_type'],
            'file_hash' => $file_info['file_hash']
        ]));

        $stmt = $pdo->prepare("
            INSERT INTO documents (
                id, user_id, original_name, storage_path, file_name,
                file_size, file_type, file_hash, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");

        $result = $stmt->execute([
            $file_info['id'],
            $user['user_id'],
            $file_info['original_name'],
            $file_info['relative_path'],
            $file_info['stored_name'],
            $file_info['file_size'],
            $file_info['file_type'],
            $file_info['file_hash']
        ]);

        if (!$result) {
            $error_info = $stmt->errorInfo();
            error_log("Database insertion failed: " . json_encode($error_info));
            throw new PDOException("Failed to insert document record: " . $error_info[2]);
        }

        error_log("Document record inserted successfully");

        // Update storage usage
        error_log("Updating storage usage for user: " . $user['user_id']);
        $stmt = $pdo->prepare("
            INSERT INTO storage_usage (user_id, total_used, document_count)
            VALUES (?, ?, 1)
            ON DUPLICATE KEY UPDATE
                total_used = total_used + ?,
                document_count = document_count + 1,
                last_updated = NOW()
        ");
        $storage_result = $stmt->execute([$user['user_id'], $file_info['file_size'], $file_info['file_size']]);

        if (!$storage_result) {
            $error_info = $stmt->errorInfo();
            error_log("Storage usage update failed: " . json_encode($error_info));
            // Don't throw exception here as the file is already uploaded and recorded
        } else {
            error_log("Storage usage updated successfully");
        }

    } catch (PDOException $e) {
        // If database insertion fails, remove the uploaded file
        if (file_exists($file_path)) {
            unlink($file_path);
        }
        error_log("Database error during file upload: " . $e->getMessage());
        sendErrorResponse('Failed to save file metadata', 500);
    }

    // Log upload activity
    logActivity('file_uploaded', [
        'file_id' => $file_info['id'],
        'original_name' => $file_info['original_name'],
        'file_size' => $file_info['file_size'],
        'user_id' => $user['user_id']
    ]);

    error_log("Database operations completed successfully");
    error_log("=== FILE UPLOAD DEBUG END ===");

    sendSuccessResponse($file_info, 'File uploaded successfully');

} catch (Exception $e) {
    error_log("EXCEPTION in file upload: " . $e->getMessage());
    error_log("Exception trace: " . $e->getTraceAsString());
    error_log("=== FILE UPLOAD DEBUG END (ERROR) ===");
    sendErrorResponse('File upload failed', 500);
}
