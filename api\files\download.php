<?php
/**
 * File Download API
 * Replaces Firebase Storage download functionality
 * 
 * @package MtcInvoice
 * @version 1.0
 */

require_once '../config/config.php';
require_once '../utils/jwt.php';

try {
    // Authentication required for file downloads
    $user = JWT::requireAuth(JWT_SECRET_KEY);
    error_log("Download request authenticated for user: " . $user['user_id']);

    // Get filename from query parameter
    $filename = $_GET['file'] ?? null;

    if (!$filename) {
        sendErrorResponse('Filename is required', 400);
    }

    // Decode URL encoding (handle %20 spaces and other encoded characters)
    $filename = urldecode($filename);

    // Sanitize filename to prevent directory traversal
    $filename = basename($filename);

    // Additional security: check for suspicious patterns
    if (strpos($filename, '..') !== false || strpos($filename, '/') !== false || strpos($filename, '\\') !== false) {
        error_log("Suspicious filename detected: " . $filename);
        sendErrorResponse('Invalid filename', 400);
    }

    // Validate file extension
    $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    if (!in_array($file_extension, ALLOWED_FILE_TYPES)) {
        error_log("Invalid file type requested: " . $file_extension);
        sendErrorResponse('File type not allowed', 400);
    }

    // Log the decoded filename for debugging
    error_log("Download request for file: " . $filename);

    // Try multiple possible file paths
    $possible_paths = [
        UPLOAD_DIR . 'pdfs/' . $filename,
        '../uploads/pdfs/' . $filename,
        realpath('../uploads/pdfs/') . '/' . $filename
    ];

    $file_path = null;
    foreach ($possible_paths as $path) {
        error_log("Checking path: " . $path);
        if (file_exists($path)) {
            $file_path = $path;
            error_log("File found at: " . $path);
            break;
        }
    }

    // Check if file exists
    if (!$file_path || !file_exists($file_path)) {
        error_log("File not found. Searched paths: " . implode(', ', $possible_paths));
        sendErrorResponse('File not found', 404);
    }
    
    // Get file info
    $file_size = filesize($file_path);
    $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    // Set appropriate content type
    $content_types = [
        'pdf' => 'application/pdf',
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    $content_type = $content_types[$file_extension] ?? 'application/octet-stream';
    
    // Set headers for file download
    header('Content-Type: ' . $content_type);
    header('Content-Length: ' . $file_size);
    header('Content-Disposition: inline; filename="' . $filename . '"');
    header('Cache-Control: public, max-age=3600');
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
    
    // Handle range requests for large files
    $range = $_SERVER['HTTP_RANGE'] ?? null;
    
    if ($range) {
        // Parse range header
        if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
            $start = intval($matches[1]);
            $end = $matches[2] ? intval($matches[2]) : $file_size - 1;
            
            if ($start < $file_size && $end < $file_size && $start <= $end) {
                $length = $end - $start + 1;
                
                header('HTTP/1.1 206 Partial Content');
                header("Content-Range: bytes $start-$end/$file_size");
                header("Content-Length: $length");
                
                $file = fopen($file_path, 'rb');
                fseek($file, $start);
                echo fread($file, $length);
                fclose($file);
                exit;
            }
        }
    }
    
    // Log download activity
    logActivity('file_downloaded', [
        'filename' => $filename,
        'file_size' => $file_size,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ]);
    
    // Output file
    readfile($file_path);
    
} catch (Exception $e) {
    error_log("File download error: " . $e->getMessage());
    sendErrorResponse('File download failed', 500);
}
