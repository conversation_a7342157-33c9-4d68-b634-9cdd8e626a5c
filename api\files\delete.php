<?php
/**
 * File Delete API
 * Delete uploaded files (replaces Firebase Storage delete functionality)
 * 
 * @package MtcInvoice
 * @version 1.0
 */

require_once '../config/config.php';
require_once '../utils/jwt.php';

// Only allow DELETE requests
if ($_SERVER['REQUEST_METHOD'] !== 'DELETE') {
    sendErrorResponse('Method not allowed', 405);
}

try {
    // Authentication required
    $user = JWT::requireAuth(JWT_SECRET_KEY);

    // Get filename from query parameter
    $filename = $_GET['file'] ?? null;

    if (!$filename) {
        sendErrorResponse('Filename is required', 400);
    }

    // Decode URL encoding (handle %20 spaces and other encoded characters)
    $filename = urldecode($filename);

    // Sanitize filename to prevent directory traversal
    $filename = basename($filename);

    // Additional security: check for suspicious patterns
    if (strpos($filename, '..') !== false || strpos($filename, '/') !== false || strpos($filename, '\\') !== false) {
        error_log("Suspicious filename detected: " . $filename);
        sendErrorResponse('Invalid filename', 400);
    }

    // Validate file extension
    $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    if (!in_array($file_extension, ALLOWED_FILE_TYPES)) {
        error_log("Invalid file type for deletion: " . $file_extension);
        sendErrorResponse('File type not allowed', 400);
    }

    // Log the decoded filename for debugging
    error_log("Delete request for file: " . $filename);

    // Try multiple possible file paths
    $possible_paths = [
        UPLOAD_DIR . 'pdfs/' . $filename,
        '../uploads/pdfs/' . $filename,
        realpath('../uploads/pdfs/') . '/' . $filename
    ];

    $file_path = null;
    foreach ($possible_paths as $path) {
        error_log("Checking path: " . $path);
        if (file_exists($path)) {
            $file_path = $path;
            error_log("File found at: " . $path);
            break;
        }
    }

    // Check if file exists
    if (!$file_path || !file_exists($file_path)) {
        error_log("File not found. Searched paths: " . implode(', ', $possible_paths));
        sendErrorResponse('File not found', 404);
    }
    
    // Get file info before deletion
    $file_size = filesize($file_path);

    // Delete file metadata from database first
    try {
        require_once '../config/database.php';
        $database = new Database();
        $pdo = $database->getConnection();

        if ($pdo) {
            // Find the file record by filename
            $stmt = $pdo->prepare("SELECT id, file_size, user_id FROM documents WHERE file_name = ? OR original_name = ?");
            $stmt->execute([$filename, $filename]);
            $file_record = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($file_record) {
                // Delete from documents table
                $stmt = $pdo->prepare("DELETE FROM documents WHERE id = ?");
                $stmt->execute([$file_record['id']]);

                // Update storage usage
                $stmt = $pdo->prepare("
                    UPDATE storage_usage
                    SET total_used = GREATEST(0, total_used - ?),
                        document_count = GREATEST(0, document_count - 1),
                        last_updated = NOW()
                    WHERE user_id = ?
                ");
                $stmt->execute([$file_record['file_size'], $file_record['user_id']]);

                error_log("File metadata deleted from database for: " . $filename);
            } else {
                error_log("No database record found for file: " . $filename);
            }
        }
    } catch (Exception $e) {
        error_log("Database deletion error: " . $e->getMessage());
        // Continue with file deletion even if database update fails
    }

    // Delete physical file
    if (!unlink($file_path)) {
        sendErrorResponse('Failed to delete file', 500);
    }

    // Log deletion activity
    logActivity('file_deleted', [
        'filename' => $filename,
        'file_size' => $file_size,
        'user_id' => $user['user_id']
    ]);

    sendSuccessResponse(null, 'File deleted successfully');
    
} catch (Exception $e) {
    error_log("File delete error: " . $e->getMessage());
    sendErrorResponse('File deletion failed', 500);
}
