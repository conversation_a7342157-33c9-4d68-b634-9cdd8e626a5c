<?php
/**
 * Test File API Fixes
 * Validate that the 404 and 500 errors have been resolved
 */

require_once '../api/config/config.php';

echo "<h1>File API Fixes Test</h1>\n";
echo "<pre>\n";

echo "=== TESTING FILE API ENDPOINTS ===\n";
echo "Base URL: " . BASE_URL . "\n";
echo "Upload Directory: " . UPLOAD_DIR . "pdfs/\n\n";

// Test 1: Check if files exist in upload directory
echo "=== TEST 1: PHYSICAL FILE EXISTENCE ===\n";
$upload_dir = '../uploads/pdfs/';
if (is_dir($upload_dir)) {
    $files = scandir($upload_dir);
    $pdf_files = array_filter($files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'pdf';
    });
    
    echo "Found " . count($pdf_files) . " PDF files:\n";
    foreach ($pdf_files as $file) {
        echo "  - " . $file . " (" . filesize($upload_dir . $file) . " bytes)\n";
    }
    
    // Test URL encoding scenarios
    $test_filename = "Order Details-1-1_1749996936.pdf";
    $encoded_filename = urlencode($test_filename);
    
    echo "\nTesting filename encoding:\n";
    echo "Original: " . $test_filename . "\n";
    echo "URL Encoded: " . $encoded_filename . "\n";
    echo "File exists (original): " . (file_exists($upload_dir . $test_filename) ? 'YES' : 'NO') . "\n";
    echo "File exists (decoded): " . (file_exists($upload_dir . urldecode($encoded_filename)) ? 'YES' : 'NO') . "\n";
    
} else {
    echo "❌ Upload directory not found: " . $upload_dir . "\n";
}

// Test 2: Database connection and records
echo "\n=== TEST 2: DATABASE RECORDS ===\n";
try {
    require_once '../api/config/database.php';
    $database = new Database();
    $pdo = $database->getConnection();
    
    if ($pdo) {
        echo "✅ Database connection successful\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM documents");
        $result = $stmt->fetch();
        echo "Documents in database: " . $result['count'] . "\n";
        
        // Check for specific problematic files
        $stmt = $pdo->prepare("SELECT original_name, file_name, storage_path FROM documents WHERE original_name LIKE '%Order Details%' OR file_name LIKE '%Order Details%'");
        $stmt->execute();
        $problem_files = $stmt->fetchAll();
        
        if (!empty($problem_files)) {
            echo "\nProblematic files in database:\n";
            foreach ($problem_files as $file) {
                echo "  Original: " . $file['original_name'] . "\n";
                echo "  Stored: " . $file['file_name'] . "\n";
                echo "  Path: " . $file['storage_path'] . "\n";
                echo "  ---\n";
            }
        }
        
    } else {
        echo "❌ Database connection failed\n";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

// Test 3: API endpoint accessibility
echo "\n=== TEST 3: API ENDPOINT TESTS ===\n";

function testEndpoint($url, $method = 'GET', $headers = []) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'http_code' => $http_code,
        'response' => $response,
        'error' => $error
    ];
}

// Test download endpoint with URL encoded filename
$test_file = "Order%20Details-1-1_1749996936.pdf";
$download_url = BASE_URL . "files/download.php?file=" . $test_file;

echo "Testing download endpoint:\n";
echo "URL: " . $download_url . "\n";

$result = testEndpoint($download_url);
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Error: " . ($result['error'] ?: 'None') . "\n";

if ($result['http_code'] === 401) {
    echo "✅ Authentication required (expected)\n";
} elseif ($result['http_code'] === 404) {
    echo "❌ File not found (404 error still exists)\n";
} elseif ($result['http_code'] === 200) {
    echo "✅ File found and accessible\n";
} else {
    echo "⚠️  Unexpected response code\n";
}

// Test delete endpoint
$delete_url = BASE_URL . "files/delete.php?file=" . $test_file;
echo "\nTesting delete endpoint:\n";
echo "URL: " . $delete_url . "\n";

$result = testEndpoint($delete_url, 'DELETE');
echo "HTTP Code: " . $result['http_code'] . "\n";

if ($result['http_code'] === 401) {
    echo "✅ Authentication required (expected)\n";
} elseif ($result['http_code'] === 405) {
    echo "❌ Method not allowed (check if DELETE is properly handled)\n";
} else {
    echo "Response: " . substr($result['response'], 0, 200) . "\n";
}

// Test 4: Configuration validation
echo "\n=== TEST 4: CONFIGURATION VALIDATION ===\n";
echo "MAX_FILE_SIZE: " . (MAX_FILE_SIZE / 1024 / 1024) . " MB\n";
echo "ALLOWED_FILE_TYPES: " . implode(', ', ALLOWED_FILE_TYPES) . "\n";
echo "JWT_SECRET_KEY: " . (defined('JWT_SECRET_KEY') ? 'Defined' : 'Not defined') . "\n";

// Test 5: Directory permissions
echo "\n=== TEST 5: DIRECTORY PERMISSIONS ===\n";
$dirs_to_check = [
    '../uploads/',
    '../uploads/pdfs/',
    UPLOAD_DIR,
    UPLOAD_DIR . 'pdfs/'
];

foreach ($dirs_to_check as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        $writable = is_writable($dir) ? 'YES' : 'NO';
        echo "✅ " . $dir . " - Permissions: " . $perms . " - Writable: " . $writable . "\n";
    } else {
        echo "❌ " . $dir . " - Does not exist\n";
    }
}

echo "\n=== SUMMARY ===\n";
echo "1. Check the HTTP response codes above\n";
echo "2. 401 responses are expected (authentication required)\n";
echo "3. 404 responses indicate the filename encoding fix may need adjustment\n";
echo "4. 500 responses indicate server errors that need investigation\n";
echo "5. Physical files should match database records\n";

echo "</pre>\n";
?>
